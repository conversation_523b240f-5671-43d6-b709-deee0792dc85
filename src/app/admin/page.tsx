"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  FileText, 
  Heart, 
  Calendar, 
  Settings, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  PlusCircle,
  Eye,
  Edit,
  BarChart3,
  UserCheck,
  Home,
  Car
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface DashboardStats {
  totalPets: number
  availablePets: number
  pendingApplications: number
  approvedApplications: number
  totalVolunteers: number
  activeVolunteers: number
  totalFosters: number
  activeFosters: number
  recentApplications: Array<{
    id: string
    status: string
    submittedAt: string
    pet: {
      name: string
      species: string
    }
    user: {
      name: string
      email: string
    }
  }>
  recentPets: Array<{
    id: string
    name: string
    species: string
    breed: string
    status: string
    arrivalDate: string
  }>
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      // Check if user has admin/staff permissions
      if (!["STAFF", "ADMIN"].includes(session.user.role)) {
        toast.error("Access denied. Staff permissions required.")
        router.push("/dashboard")
        return
      }

      fetchDashboardStats()
    }
  }, [status, session, router])

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch("/api/admin/dashboard")
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      } else {
        toast.error("Failed to load dashboard data")
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "rejected":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "under_review":
      case "reference_check":
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <FileText className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800"
      case "rejected":
        return "bg-red-100 text-red-800"
      case "under_review":
      case "reference_check":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (status === "loading" || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Failed to Load Dashboard</h1>
        <Button onClick={fetchDashboardStats}>Retry</Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Staff Dashboard
        </h1>
        <p className="text-gray-600">
          Manage pets, applications, volunteers, and foster families.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Link href="/admin/pets/new">
          <Button className="w-full h-16 flex flex-col items-center justify-center">
            <PlusCircle className="h-5 w-5 mb-1" />
            Add Pet
          </Button>
        </Link>
        <Link href="/admin/applications">
          <Button variant="outline" className="w-full h-16 flex flex-col items-center justify-center">
            <FileText className="h-5 w-5 mb-1" />
            Review Apps
          </Button>
        </Link>
        <Link href="/admin/volunteers">
          <Button variant="outline" className="w-full h-16 flex flex-col items-center justify-center">
            <Users className="h-5 w-5 mb-1" />
            Volunteers
          </Button>
        </Link>
        <Link href="/admin/reports">
          <Button variant="outline" className="w-full h-16 flex flex-col items-center justify-center">
            <BarChart3 className="h-5 w-5 mb-1" />
            Reports
          </Button>
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.totalPets}</p>
                <p className="text-sm text-gray-600">Total Pets</p>
                <p className="text-xs text-green-600">{stats.availablePets} available</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.pendingApplications}</p>
                <p className="text-sm text-gray-600">Pending Apps</p>
                <p className="text-xs text-blue-600">{stats.approvedApplications} approved</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.totalVolunteers}</p>
                <p className="text-sm text-gray-600">Volunteers</p>
                <p className="text-xs text-green-600">{stats.activeVolunteers} active</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Home className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{stats.totalFosters}</p>
                <p className="text-sm text-gray-600">Foster Families</p>
                <p className="text-xs text-purple-600">{stats.activeFosters} active</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Applications */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Applications</CardTitle>
              <Link href="/admin/applications">
                <Button variant="outline" size="sm">View All</Button>
              </Link>
            </div>
            <CardDescription>
              Latest adoption applications requiring review
            </CardDescription>
          </CardHeader>
          <CardContent>
            {stats.recentApplications.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No recent applications</p>
              </div>
            ) : (
              <div className="space-y-4">
                {stats.recentApplications.map((application) => (
                  <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(application.status)}
                      <div>
                        <h4 className="font-semibold">{application.user.name}</h4>
                        <p className="text-sm text-gray-600">
                          {application.pet.name} • {formatDate(application.submittedAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(application.status)}>
                        {application.status.replace('_', ' ')}
                      </Badge>
                      <Link href={`/admin/applications/${application.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Pets */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Pets</CardTitle>
              <Link href="/admin/pets">
                <Button variant="outline" size="sm">View All</Button>
              </Link>
            </div>
            <CardDescription>
              Recently added pets in the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            {stats.recentPets.length === 0 ? (
              <div className="text-center py-8">
                <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No recent pets</p>
              </div>
            ) : (
              <div className="space-y-4">
                {stats.recentPets.map((pet) => (
                  <div key={pet.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-semibold">{pet.name}</h4>
                      <p className="text-sm text-gray-600">
                        {pet.breed} • Arrived {formatDate(pet.arrivalDate)}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{pet.status}</Badge>
                      <Link href={`/admin/pets/${pet.id}`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
