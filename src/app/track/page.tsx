"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  MapPin, 
  Truck, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Navigation,
  Phone,
  Mail,
  Calendar,
  Route,
  Package,
  Heart,
  Camera,
  Thermometer,
  Activity,
  Bell,
  Share2,
  Download,
  Star,
  MessageCircle,
  Zap,
  Shield,
  Wifi,
  Battery,
  Signal,
  RefreshCw,
  Eye,
  PlayCircle,
  PauseCircle,
  Volume2,
  VolumeX,
  Maximize,
  BarChart3,
  TrendingUp,
  Globe,
  Satellite,
  Radio
} from "lucide-react"
import { toast } from "react-hot-toast"

interface TrackingInfo {
  trackingNumber: string
  pet: {
    name: string
    species: string
    breed: string
    photo?: string
    age: string
    weight: string
    temperament: string
    specialNeeds?: string
  }
  status: string
  currentLocation: {
    address: string
    city: string
    state: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  origin: {
    name: string
    address: string
    city: string
    state: string
  }
  destination: {
    name: string
    address: string
    city: string
    state: string
  }
  estimatedArrival: string
  driver: {
    name: string
    phone: string
    email: string
    rating: number
    experience: string
    photo?: string
  }
  vehicle: {
    make: string
    model: string
    year: number
    licensePlate: string
    color: string
  }
  liveData: {
    temperature: number
    humidity: number
    speed: number
    batteryLevel: number
    signalStrength: number
    lastUpdate: string
  }
  petHealth: {
    heartRate: number
    activity: string
    stress: string
    lastFed: string
    lastWalk: string
  }
  updates: Array<{
    timestamp: string
    location: string
    status: string
    notes?: string
    photo?: string
    video?: string
  }>
  notifications: Array<{
    id: string
    type: 'info' | 'warning' | 'success' | 'error'
    message: string
    timestamp: string
    read: boolean
  }>
}

// Mock tracking data
const mockTrackingData: { [key: string]: TrackingInfo } = {
  "TRK001234": {
    trackingNumber: "TRK001234",
    pet: {
      name: "Buddy",
      species: "Dog",
      breed: "Golden Retriever",
      photo: "/images/pets/buddy.jpg",
      age: "3 years",
      weight: "65 lbs",
      temperament: "Friendly & Energetic",
      specialNeeds: "Needs medication every 8 hours"
    },
    status: "IN_TRANSIT",
    currentLocation: {
      address: "Highway 101, Mile Marker 45",
      city: "Midway City",
      state: "CA",
      coordinates: {
        lat: 37.7749,
        lng: -122.4194
      }
    },
    origin: {
      name: "San Francisco Animal Shelter",
      address: "123 Shelter Lane",
      city: "San Francisco",
      state: "CA"
    },
    destination: {
      name: "Happy Homes Rescue",
      address: "456 Rescue Road",
      city: "Los Angeles",
      state: "CA"
    },
    estimatedArrival: "2024-01-16T14:30:00Z",
    driver: {
      name: "Mike Johnson",
      phone: "(555) 123-DRIVE",
      email: "<EMAIL>",
      rating: 4.9,
      experience: "5+ years",
      photo: "/images/drivers/mike.jpg"
    },
    vehicle: {
      make: "Ford",
      model: "Transit",
      year: 2023,
      licensePlate: "PET-123",
      color: "White"
    },
    liveData: {
      temperature: 72,
      humidity: 45,
      speed: 65,
      batteryLevel: 87,
      signalStrength: 95,
      lastUpdate: "2024-01-15T16:45:00Z"
    },
    petHealth: {
      heartRate: 85,
      activity: "Resting",
      stress: "Low",
      lastFed: "2024-01-15T12:00:00Z",
      lastWalk: "2024-01-15T14:30:00Z"
    },
    updates: [
      {
        timestamp: "2024-01-15T08:00:00Z",
        location: "San Francisco Animal Shelter",
        status: "PICKED_UP",
        notes: "Pet safely loaded and secured for transport",
        photo: "/images/updates/pickup.jpg"
      },
      {
        timestamp: "2024-01-15T10:30:00Z",
        location: "San Jose, CA",
        status: "IN_TRANSIT",
        notes: "Rest stop completed, pet is comfortable",
        photo: "/images/updates/rest-stop.jpg"
      },
      {
        timestamp: "2024-01-15T13:15:00Z",
        location: "Fresno, CA",
        status: "IN_TRANSIT",
        notes: "Lunch break and pet exercise completed",
        video: "/videos/updates/exercise.mp4"
      },
      {
        timestamp: "2024-01-15T16:45:00Z",
        location: "Bakersfield, CA",
        status: "IN_TRANSIT",
        notes: "On schedule, pet doing well",
        photo: "/images/updates/happy-buddy.jpg"
      }
    ],
    notifications: [
      {
        id: "1",
        type: "success",
        message: "Buddy just had his afternoon walk and is doing great!",
        timestamp: "2024-01-15T16:45:00Z",
        read: false
      },
      {
        id: "2",
        type: "info",
        message: "Next medication due in 2 hours",
        timestamp: "2024-01-15T16:00:00Z",
        read: false
      },
      {
        id: "3",
        type: "success",
        message: "Temperature and humidity levels optimal",
        timestamp: "2024-01-15T15:30:00Z",
        read: true
      }
    ]
  },
  "TRK005678": {
    trackingNumber: "TRK005678",
    pet: {
      name: "Luna",
      species: "Cat",
      breed: "Persian",
      photo: "/images/pets/luna.jpg",
      age: "2 years",
      weight: "8 lbs",
      temperament: "Calm & Gentle"
    },
    status: "DELIVERED",
    currentLocation: {
      address: "789 Forever Home Street",
      city: "Portland",
      state: "OR",
      coordinates: {
        lat: 45.5152,
        lng: -122.6784
      }
    },
    origin: {
      name: "Seattle Cat Rescue",
      address: "321 Cat Avenue",
      city: "Seattle",
      state: "WA"
    },
    destination: {
      name: "Portland Pet Sanctuary",
      address: "789 Forever Home Street",
      city: "Portland",
      state: "OR"
    },
    estimatedArrival: "2024-01-14T16:00:00Z",
    driver: {
      name: "Sarah Williams",
      phone: "(555) 456-PETS",
      email: "<EMAIL>",
      rating: 5.0,
      experience: "8+ years",
      photo: "/images/drivers/sarah.jpg"
    },
    vehicle: {
      make: "Mercedes",
      model: "Sprinter",
      year: 2022,
      licensePlate: "CAT-456",
      color: "Silver"
    },
    liveData: {
      temperature: 70,
      humidity: 40,
      speed: 0,
      batteryLevel: 100,
      signalStrength: 100,
      lastUpdate: "2024-01-14T16:00:00Z"
    },
    petHealth: {
      heartRate: 120,
      activity: "Sleeping",
      stress: "Very Low",
      lastFed: "2024-01-14T14:00:00Z",
      lastWalk: "N/A"
    },
    updates: [
      {
        timestamp: "2024-01-14T09:00:00Z",
        location: "Seattle Cat Rescue",
        status: "PICKED_UP",
        notes: "Luna is comfortable in her carrier",
        photo: "/images/updates/luna-pickup.jpg"
      },
      {
        timestamp: "2024-01-14T12:30:00Z",
        location: "Olympia, WA",
        status: "IN_TRANSIT",
        notes: "Midway rest stop completed",
        photo: "/images/updates/luna-rest.jpg"
      },
      {
        timestamp: "2024-01-14T15:45:00Z",
        location: "Portland Pet Sanctuary",
        status: "DELIVERED",
        notes: "Luna safely delivered and settled in",
        video: "/videos/updates/luna-delivery.mp4"
      }
    ],
    notifications: [
      {
        id: "1",
        type: "success",
        message: "Luna has been successfully delivered to her new home!",
        timestamp: "2024-01-14T16:00:00Z",
        read: false
      },
      {
        id: "2",
        type: "info",
        message: "Transport completed successfully",
        timestamp: "2024-01-14T15:45:00Z",
        read: true
      }
    ]
  }
}

const statusColors = {
  SCHEDULED: "bg-blue-100 text-blue-800",
  PICKED_UP: "bg-yellow-100 text-yellow-800",
  IN_TRANSIT: "bg-purple-100 text-purple-800",
  DELIVERED: "bg-green-100 text-green-800",
  DELAYED: "bg-red-100 text-red-800"
}

const statusIcons = {
  SCHEDULED: Clock,
  PICKED_UP: Package,
  IN_TRANSIT: Truck,
  DELIVERED: CheckCircle,
  DELAYED: AlertCircle
}

export default function TrackPage() {
  const [trackingNumber, setTrackingNumber] = useState("")
  const [trackingInfo, setTrackingInfo] = useState<TrackingInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [notFound, setNotFound] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [notifications, setNotifications] = useState<TrackingInfo['notifications']>([])
  const [showNotifications, setShowNotifications] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState<{type: 'photo' | 'video', url: string} | null>(null)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [liveDataHistory, setLiveDataHistory] = useState<Array<{timestamp: string, speed: number, temperature: number}>>([])
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && trackingInfo && trackingInfo.status === 'IN_TRANSIT') {
      intervalRef.current = setInterval(() => {
        // Simulate live data updates
        const newDataPoint = {
          timestamp: new Date().toISOString(),
          speed: Math.floor(Math.random() * 20) + 50, // 50-70 mph
          temperature: Math.floor(Math.random() * 6) + 70 // 70-76°F
        }
        setLiveDataHistory(prev => [...prev.slice(-19), newDataPoint])
        
        // Simulate new notifications
        if (Math.random() < 0.3) { // 30% chance of new notification
          const newNotification = {
            id: Date.now().toString(),
            type: 'info' as const,
            message: `Speed: ${newDataPoint.speed} mph, Temperature: ${newDataPoint.temperature}°F`,
            timestamp: new Date().toISOString(),
            read: false
          }
          setNotifications(prev => [newNotification, ...prev.slice(0, 9)])
          
          if (soundEnabled) {
            // Play notification sound (would be actual audio in real app)
            console.log('🔔 Notification sound')
          }
        }
      }, 5000) // Update every 5 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoRefresh, trackingInfo, soundEnabled])

  const handleTrack = async () => {
    if (!trackingNumber.trim()) {
      toast.error("Please enter a tracking number")
      return
    }

    setLoading(true)
    setNotFound(false)
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const info = mockTrackingData[trackingNumber.toUpperCase()]
    
    if (info) {
      setTrackingInfo(info)
      setNotifications(info.notifications)
      setNotFound(false)
      
      // Initialize live data history
      const initialHistory = Array.from({length: 10}, (_, i) => ({
        timestamp: new Date(Date.now() - (9-i) * 60000).toISOString(),
        speed: Math.floor(Math.random() * 20) + 50,
        temperature: Math.floor(Math.random() * 6) + 70
      }))
      setLiveDataHistory(initialHistory)
      
      toast.success(`Tracking ${info.pet.name}'s journey!`)
    } else {
      setTrackingInfo(null)
      setNotFound(true)
      toast.error("Tracking number not found")
    }
    
    setLoading(false)
  }

  const shareTracking = () => {
    if (trackingInfo) {
      navigator.clipboard.writeText(`Track ${trackingInfo.pet.name}'s journey: ${window.location.href}`)
      toast.success("Tracking link copied to clipboard!")
    }
  }

  const downloadReport = () => {
    if (trackingInfo) {
      toast.success("Downloading tracking report...")
      // In real app, would generate and download PDF
    }
  }

  const markNotificationRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => notif.id === id ? {...notif, read: true} : notif)
    )
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle
      case 'warning': return AlertCircle
      case 'error': return AlertCircle
      default: return Bell
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'error': return 'text-red-600'
      default: return 'text-blue-600'
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getEstimatedArrival = (dateString: string) => {
    const now = new Date()
    const arrival = new Date(dateString)
    const diffMs = arrival.getTime() - now.getTime()
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60))
    
    if (diffHours <= 0) {
      return "Arrived"
    } else if (diffHours < 24) {
      return `${diffHours} hours`
    } else {
      const diffDays = Math.ceil(diffHours / 24)
      return `${diffDays} days`
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-3xl"></div>
          <div className="relative">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              🐾 Track Your Pet's Journey
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Real-time tracking with live updates, health monitoring, and instant notifications
            </p>
            <div className="flex justify-center items-center gap-4 mt-6">
              <Badge className="bg-green-100 text-green-800 px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Live Updates
              </Badge>
              <Badge className="bg-blue-100 text-blue-800 px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Secure Tracking
              </Badge>
              <Badge className="bg-purple-100 text-purple-800 px-3 py-1">
                <Heart className="h-4 w-4 mr-1" />
                Pet Health Monitor
              </Badge>
            </div>
          </div>
        </div>

        {/* Tracking Input */}
        <Card className="max-w-2xl mx-auto mb-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <CardTitle className="text-2xl font-bold text-gray-800">Enter Tracking Number</CardTitle>
            <CardDescription>
              Your tracking number was provided when the transport was scheduled
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <div className="flex-1 relative">
                <Label htmlFor="trackingNumber" className="sr-only">Tracking Number</Label>
                <Input
                  id="trackingNumber"
                  placeholder="Enter tracking number (e.g., TRK001234)"
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleTrack()}
                  className="pl-10 h-12 text-lg border-2 border-gray-200 focus:border-blue-500 transition-colors"
                />
                <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <Button 
                onClick={handleTrack} 
                disabled={loading}
                className="h-12 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg"
              >
                {loading ? (
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                ) : (
                  <Search className="h-5 w-5 mr-2" />
                )}
                Track
              </Button>
            </div>
            
            {/* Sample tracking numbers */}
            <div className="text-sm text-gray-600">
              <p className="mb-3 font-medium">Try these demo tracking numbers:</p>
              <div className="flex gap-2 flex-wrap">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setTrackingNumber("TRK001234")}
                  className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                >
                  <Truck className="h-3 w-3 mr-1" />
                  TRK001234 (In Transit)
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setTrackingNumber("TRK005678")}
                  className="hover:bg-green-50 hover:border-green-300 transition-colors"
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  TRK005678 (Delivered)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tracking Results */}
        {trackingInfo && (
          <div className="max-w-6xl mx-auto space-y-8">
            {/* Current Status */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Package className="h-5 w-5 mr-2" />
                      Tracking #{trackingInfo.trackingNumber}
                    </CardTitle>
                    <CardDescription>
                      Transport for {trackingInfo.pet.name}
                    </CardDescription>
                  </div>
                  <Badge className={statusColors[trackingInfo.status as keyof typeof statusColors]}>
                    {React.createElement(statusIcons[trackingInfo.status as keyof typeof statusIcons], { className: "h-4 w-4 mr-1" })}
                    {formatStatus(trackingInfo.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  {/* Pet Info */}
                  <div className="text-center">
                    <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-3 flex items-center justify-center">
                      {trackingInfo.pet.photo ? (
                        <img
                          src={trackingInfo.pet.photo}
                          alt={trackingInfo.pet.name}
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : (
                        <span className="text-2xl">🐾</span>
                      )}
                    </div>
                    <h3 className="font-semibold">{trackingInfo.pet.name}</h3>
                    <p className="text-sm text-gray-600">{trackingInfo.pet.breed} • {trackingInfo.pet.species}</p>
                  </div>

                  {/* Current Location */}
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      Current Location
                    </h4>
                    <p className="text-sm text-gray-600">
                      {trackingInfo.currentLocation.address}<br />
                      {trackingInfo.currentLocation.city}, {trackingInfo.currentLocation.state}
                    </p>
                  </div>

                  {/* ETA */}
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      Estimated Arrival
                    </h4>
                    <p className="text-sm text-gray-600">
                      {formatDateTime(trackingInfo.estimatedArrival)}<br />
                      <span className="text-blue-600 font-medium">
                        {getEstimatedArrival(trackingInfo.estimatedArrival)}
                      </span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* Route Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Route className="h-5 w-5 mr-2" />
                    Route Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-green-600 mb-1">Origin</h4>
                    <p className="text-sm">
                      {trackingInfo.origin.name}<br />
                      {trackingInfo.origin.address}<br />
                      {trackingInfo.origin.city}, {trackingInfo.origin.state}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-center py-2">
                    <div className="flex-1 border-t border-dashed border-gray-300"></div>
                    <Truck className="h-6 w-6 text-blue-500 mx-4" />
                    <div className="flex-1 border-t border-dashed border-gray-300"></div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-blue-600 mb-1">Destination</h4>
                    <p className="text-sm">
                      {trackingInfo.destination.name}<br />
                      {trackingInfo.destination.address}<br />
                      {trackingInfo.destination.city}, {trackingInfo.destination.state}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Driver Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Navigation className="h-5 w-5 mr-2" />
                    Driver Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">{trackingInfo.driver.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <a href={`tel:${trackingInfo.driver.phone}`} className="text-blue-600 hover:underline">
                          {trackingInfo.driver.phone}
                        </a>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                        <a href={`mailto:${trackingInfo.driver.email}`} className="text-blue-600 hover:underline">
                          {trackingInfo.driver.email}
                        </a>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-sm text-blue-800">
                      <strong>Need to contact the driver?</strong><br />
                      Feel free to call or email for updates on your pet's journey.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Tracking Updates */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Tracking Updates
                </CardTitle>
                <CardDescription>
                  Real-time updates on your pet's journey
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trackingInfo.updates.map((update, index) => (
                    <div key={index} className="flex items-start space-x-4 pb-4 border-b border-gray-200 last:border-b-0">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          {React.createElement(statusIcons[update.status as keyof typeof statusIcons] || MapPin, { 
                            className: "h-4 w-4 text-blue-600" 
                          })}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="text-sm font-semibold text-gray-900">
                            {formatStatus(update.status)}
                          </h4>
                          <span className="text-xs text-gray-500">
                            {formatDateTime(update.timestamp)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">{update.location}</p>
                        {update.notes && (
                          <p className="text-sm text-gray-500 italic">{update.notes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Map Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Live Map
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <MapPin className="h-12 w-12 mx-auto mb-2" />
                    <p>Interactive map showing real-time location</p>
                    <p className="text-sm">Coordinates: {trackingInfo.currentLocation.coordinates.lat}, {trackingInfo.currentLocation.coordinates.lng}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Not Found */}
        {notFound && (
          <Card className="max-w-2xl mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Tracking Number Not Found</h3>
              <p className="text-gray-600 mb-6">
                Please check your tracking number and try again. If you continue to have issues, 
                contact our support team.
              </p>
              <Button variant="outline">
                <Phone className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
